# Generated migration for workflow performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0002_initial'),
    ]

    operations = [
        # Add indexes for workflow status queries optimization
        migrations.RunSQL(
            sql=[
                # Index for campaign lookup in workflow status
                "CREATE INDEX IF NOT EXISTS workflow_campaign_idx ON campaign_workflow_state (campaign_id);",
                
                # Index for current step filtering
                "CREATE INDEX IF NOT EXISTS workflow_current_step_idx ON campaign_workflow_state (current_step);",
                
                # Index for updated_at ordering
                "CREATE INDEX IF NOT EXISTS workflow_updated_at_idx ON campaign_workflow_state (updated_at);",
                
                # Composite index for common query patterns
                "CREATE INDEX IF NOT EXISTS workflow_campaign_step_idx ON campaign_workflow_state (campaign_id, current_step);",
                
                # Index for campaign filtering by HR manager (for bulk queries)
                "CREATE INDEX IF NOT EXISTS campaigns_hr_manager_idx ON campaigns_campaign (hr_manager_id);",
                
                # Composite index for campaign + hr_manager queries
                "CREATE INDEX IF NOT EXISTS campaigns_hr_manager_created_idx ON campaigns_campaign (hr_manager_id, created_at);",
            ],
            reverse_sql=[
                "DROP INDEX IF EXISTS workflow_campaign_idx;",
                "DROP INDEX IF EXISTS workflow_current_step_idx;",
                "DROP INDEX IF EXISTS workflow_updated_at_idx;",
                "DROP INDEX IF EXISTS workflow_campaign_step_idx;",
                "DROP INDEX IF EXISTS campaigns_hr_manager_idx;",
                "DROP INDEX IF EXISTS campaigns_hr_manager_created_idx;",
            ]
        ),
    ]
